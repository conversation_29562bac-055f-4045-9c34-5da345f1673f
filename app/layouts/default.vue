<script setup lang="ts">
// Sidebar state management
const isSidebarOpen = ref(false);
const sidebarRef = ref();

// Toggle sidebar function
const toggleSidebar = () => {
    isSidebarOpen.value = !isSidebarOpen.value;
};
</script>

<template>
    <div class="flex flex-col min-h-screen">
        <div
            class="h-16 w-full flex items-center justify-between gap-4 px-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900"
        >
            <div class="flex items-center gap-4">
                <UButton
                    icon="i-lucide-menu"
                    color="neutral"
                    variant="ghost"
                    size="sm"
                    square
                    class="lg:hidden"
                    @click="toggleSidebar"
                />
                <UIcon
                    name="i-custom-sensehawk-logo"
                    class="h-8 w-8 animate-fade-in"
                />
            </div>
            <div class="flex items-center gap-2">
                <UserMenu />
            </div>
        </div>

        <!-- Main content area with sidebar -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar Component -->
            <AppSidebar
                ref="sidebarRef"
                v-model:is-open="isSidebarOpen"
                @toggle="toggleSidebar"
            />

            <!-- Main content -->
            <div class="flex-1 overflow-auto">
                <slot />
            </div>
        </div>
    </div>
</template>

<style lang="scss">
[data-active] {
    background-color: var(--color-blue-100);
}

.dark {
    [data-active] {
        background-color: oklch(0.278 0.033 256.848);
    }
}
</style>
