<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui';

const navigationItems = ref<NavigationMenuItem[]>([
    {
        label: 'Dashboard',
        icon: 'i-lucide-layout-dashboard',
        to: '/',
    },
    {
        label: 'Projects',
        icon: 'i-lucide-folder',
        to: '/projects',
    },
    {
        label: 'Analytics',
        icon: 'i-lucide-chart-line',
        to: '/analytics',
    },
    {
        label: 'Settings',
        icon: 'i-lucide-settings',
        to: '/settings',
    },
]);

// Screen size detection
const isDesktop = ref(false);
const isSidebarOpen = ref(false);
const isCollapsed = computed(() => !isSidebarOpen.value);

// Initialize screen size detection and sidebar state
const initializeSidebar = () => {
    if (!import.meta.client) return;

    const mediaQuery = window.matchMedia('(min-width: 1024px)'); // lg breakpoint
    isDesktop.value = mediaQuery.matches;

    // Set initial sidebar state: open on desktop, closed on mobile
    isSidebarOpen.value = isDesktop.value;

    // Listen for screen size changes
    const handleScreenChange = (e: MediaQueryListEvent) => {
        isDesktop.value = e.matches;
        // Auto-open on desktop, auto-close on mobile
        isSidebarOpen.value = isDesktop.value;
    };

    mediaQuery.addEventListener('change', handleScreenChange);

    // Return cleanup function
    return () => {
        mediaQuery.removeEventListener('change', handleScreenChange);
    };
};

// Close sidebar on route change (mobile only)
const router = useRouter();
const closeSidebarOnNavigation = () => {
    if (!isDesktop.value) {
        isSidebarOpen.value = false;
    }
};

// Initialize on mount
onMounted(() => {
    initializeSidebar();

    // Listen for route changes to close sidebar on mobile
    router.afterEach(closeSidebarOnNavigation);
});
</script>

<template>
    <div class="flex flex-col min-h-screen">
        <div
            class="h-16 w-full flex items-center justify-between gap-4 px-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900"
        >
            <div class="flex items-center gap-4">
                <UButton
                    icon="i-lucide-menu"
                    color="neutral"
                    variant="ghost"
                    size="sm"
                    square
                    class="lg:hidden"
                    @click="isSidebarOpen = !isSidebarOpen"
                />
                <UIcon
                    name="i-custom-sensehawk-logo"
                    class="h-8 w-8 animate-fade-in"
                />
            </div>
            <div class="flex items-center gap-2">
                <UserMenu />
            </div>
        </div>

        <!-- Main content area with sidebar -->
        <div class="flex flex-1 overflow-hidden">
            <!-- Sidebar with NavigationMenu -->
            <div
                :class="[
                    'transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800',
                    isDesktop ? 'w-64' : (isSidebarOpen ? 'w-64' : 'w-0'),
                    'lg:relative absolute z-10 h-full',
                ]"
            >
                <div
                    v-if="isDesktop || isSidebarOpen"
                    class="flex flex-col h-[calc(100vh-4rem)]"
                >
                    <UNavigationMenu
                        orientation="vertical"
                        highlight
                        class="h-full py-2 px-2"
                        color="primary"
                        :items="navigationItems"
                        :collapsed="isCollapsed"
                        :ui="{
                            item: '[&>*]:h-12',
                        }"
                    />
                </div>
            </div>

            <!-- Main content -->
            <div class="flex-1 overflow-auto">
                <slot />
            </div>
        </div>

        <!-- Overlay for mobile when sidebar is open -->
        <div
            v-if="isSidebarOpen && !isDesktop"
            class="fixed inset-0 bg-black bg-opacity-50 z-0 lg:hidden"
            @click="isSidebarOpen = false"
        />
    </div>
</template>

<style lang="scss">
[data-active] {
    background-color: var(--color-blue-100);
}

.dark {
    [data-active] {
        background-color: oklch(0.278 0.033 256.848);
    }
}
</style>
